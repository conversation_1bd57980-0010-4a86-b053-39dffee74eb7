const moment = require('moment');

class MemberValidationService {
    constructor (DataService, SportEngineUtils) {
        this.DataService = DataService;
        /**
         * @type SportEngineUtilsService
         */
        this.Utils = SportEngineUtils;
    }

    get GENDER_REG_EXP () {
        return /^(?:male|female|unspecified|non-binary)$/;
    }

    get FRONTEND_FIELD_NAMES () {
        return {
            [this.MEMBER_TYPE.clubDirector]: {
                first: 'director_first',
                last: 'director_last',
                gender: 'director_gender',
                birthdate: 'director_birthdate',
                region: 'region',
                clubUsavCode: 'usav_code'
            },
            [this.MEMBER_TYPE.junior]: {
                first: 'first_name',
                last: 'last_name',
                gender: 'gender'
            },
            [this.MEMBER_TYPE.official]: {
                first: 'first_name',
                last: 'last_name',
                gender: 'gender',
                birthdate: 'birthdate',
                region: 'region'
            }
        }
    }

    get MEMBER_VALIDATION_ERROR_TEXT () {
        let errorMessage = '';

        if(this.__errorFields.length) {
            errorMessage = `Entered information doesn’t match USAV data for your USAV Membership. 
	            Please check these fields: ${this.__errorFields.join(', ')}.`
        }

        if(this.__hasExpiredMembership) {
            errorMessage += ` USAV Member Status not "Eligible"`;
        }

        return errorMessage;
    }

    get MEMBER_TYPE() {
        return {
            official: 'official',
            clubDirector: 'clubDirector',
            junior: 'junior'
        };
    }

    async processMember (memberData, memberType) {
        let memberID = memberData.usav_code;
        let birthdate = memberData.birthdate;

        if(!memberID) {
            throw new Error('Member USAV Code required');
        }

        if(!birthdate) {
            throw new Error('Member birthdate required');
        }

        let filters = {
            [this.Utils.SE_FIELDS.USAV_CODE]: memberID,
            [this.Utils.SE_FIELDS.BIRTHDATE]: birthdate.date
        }

        let seMemberData = await this.__getMembershipData(filters, memberData);

        if(_.isEmpty(seMemberData)) {
            throw { validation: 'SportEngine member not found' };
        }

        let validationError = this.__validateMemberData(memberData, seMemberData, memberType);

        return { memberData: seMemberData, validationError };
    }

    async processCanadianMember (memberData) {
        const clubCode = memberData.club_code;
        const region = memberData.region;

        if(!clubCode) {
            throw new Error('Member Club Code required');
        }

        if(!region) {
            throw new Error('Member Region required');
        }

        const filters = {
            [this.Utils.SE_FIELDS.CLUB_CODE]: clubCode,
            [this.Utils.SE_FIELDS.USAV_REGION]: region
        }

        const seMemberData = await this.__getMembershipData(filters, memberData);

        if(_.isEmpty(seMemberData)) {
            throw { validation: 'SportEngine member not found' };
        }

        return { memberData: seMemberData };
    }

    async __getMembershipData (filters, memberData) {
        let memberships = [];
        let hasNotFilteredMemberships = false;

        for await (const membership of this.DataService.getEligibility(filters)) {
            hasNotFilteredMemberships = true;

            if(memberData.region && memberData.club_code) {
                let clubCodeEqual = membership[this.Utils.SE_FIELDS.CLUB_CODE] === memberData.club_code;
                let regionEqual = membership[this.Utils.SE_FIELDS.USAV_REGION] === memberData.region;

                if(clubCodeEqual && regionEqual) {
                    memberships.push(membership);
                }
            } else {
                memberships.push(membership);
            }
        }

        if(_.isEmpty(memberships)) {
            if(hasNotFilteredMemberships && memberData.region && memberData.club_code) {
                throw { validation: 'Club Director not associated to the club in SportEngine' };
            }

            return memberships;
        }

        return this.getLastMembership(memberships);
    }

    getLastMembership (memberships) {
        let eligibleMemberships = memberships.filter(
            membership =>
                (
                    this.__isMemberStatusEligible(membership[this.Utils.SE_FIELDS.MEMBERSHIP_STATUS]) &&
                    this.__isAllowedMembershipType(membership[this.Utils.SE_FIELDS.MEMBERSHIP_NAME])
                ) || (this.__seasonalityAllowedToBeImportedAsIncomplete(membership))
        );

        memberships = !_.isEmpty(eligibleMemberships)
            ? eligibleMemberships
            : memberships;

        memberships = this.__processMembershipSeasonalityValues(memberships);

        //Filter memberships with end date value
        let membershipsWithEndDate = memberships.filter(m => !!m[this.Utils.SE_FIELDS.MEMBERSHIP_END_DATE]);

        //Return first membership if any end date found
        if(!membershipsWithEndDate.length) {
            return memberships[0];
        }

        //Return first membership if only one end date found
        if(membershipsWithEndDate.length === 1) {
            return membershipsWithEndDate[0];
        }

        //Sort memberships by end date DESC and return first array item
        return membershipsWithEndDate.sort((a, b) => {
            return moment(b[this.Utils.SE_FIELDS.MEMBERSHIP_END_DATE]).unix() -
                moment(a[this.Utils.SE_FIELDS.MEMBERSHIP_END_DATE]).unix()
        })[0];
    }

    __validateMemberData (memberData, sportEngineData, memberType) {
        this.__hasExpiredMembership = false;

        this.__errorFields = [];
        this.__frontendErrorFiedls = [];

        if (memberData.gender && !this.GENDER_REG_EXP.test(memberData.gender)) {
            throw { validation: 'Gender should be "male" or "female" or "unspecified" or "non-binary"' };
        }

        if(memberData.first && !this.__isNameEqual(memberData.first, sportEngineData[this.Utils.SE_FIELDS.FIRST])) {
            this.__errorFields.push('"First Name"');
            this.__frontendErrorFiedls.push(this.FRONTEND_FIELD_NAMES[memberType].first);
        }

        if(memberData.last && !this.__isNameEqual(memberData.last, sportEngineData[this.Utils.SE_FIELDS.LAST])) {
            this.__errorFields.push('"Last Name"');
            this.__frontendErrorFiedls.push(this.FRONTEND_FIELD_NAMES[memberType].last);
        }

        if(memberData.gender && !this.__isGenderEqual(memberData.gender, sportEngineData[this.Utils.SE_FIELDS.GENDER])) {
            this.__errorFields.push('"Gender"');
            this.__frontendErrorFiedls.push(this.FRONTEND_FIELD_NAMES[memberType].gender);
        }

        if(
            memberData.birthdate &&
            !this.__isBirthdateEqual(memberData.birthdate, sportEngineData[this.Utils.SE_FIELDS.BIRTHDATE])
        ) {
            this.__errorFields.push('"Birthdate"');
            this.__frontendErrorFiedls.push(this.FRONTEND_FIELD_NAMES[memberType].birthdate);
        }

        if(memberData.club_code && memberData.club_code !== sportEngineData[this.Utils.SE_FIELDS.CLUB_CODE]) {
            this.__errorFields.push('"Club USAV Code"');
            this.__frontendErrorFiedls.push(this.FRONTEND_FIELD_NAMES[memberType].clubUsavCode);
        }

        const isRegionRequired = memberType !== this.MEMBER_TYPE.official;
        if(
            memberData.region &&
            !this.__regionIsValid(memberData.region, sportEngineData[this.Utils.SE_FIELDS.USAV_REGION], isRegionRequired)
        ) {
            this.__errorFields.push('"Region"');
            this.__frontendErrorFiedls.push(this.FRONTEND_FIELD_NAMES[memberType].region);
        }

        if(!this.__isMemberStatusEligible(sportEngineData[this.Utils.SE_FIELDS.MEMBERSHIP_STATUS])) {
            this.__hasExpiredMembership = true;
        }

        if(this.__errorFields.length || this.__hasExpiredMembership) {
            return {
                validation          : this.MEMBER_VALIDATION_ERROR_TEXT,
                hasExpiredMembership: this.__hasExpiredMembership,
                notValidFields      : this.__frontendErrorFiedls,
            };
        }
    }

    __regionIsValid (dbRegion, seRegion, isRegionRequired) {
        if(!seRegion) {
            return !isRegionRequired;
        }
        return seRegion === dbRegion;
    }

    __isMemberStatusEligible (memberStatus) {
        return memberStatus === this.Utils.ELIGIBLE_MEMBER_STATUS;
    }

    __isAllowedMembershipType (membershipName) {
        return !this.Utils.FORBIDDEN_MEMBERSHIPS.includes(membershipName);
    }

    __isNameEqual (stringOne, stringTwo) {
        return String(stringOne).trim().toUpperCase() === String(stringTwo).trim().toUpperCase();
    }

    __isBirthdateEqual (dbBirthdate, seBirthdate) {
        seBirthdate = moment(seBirthdate, 'YYYY-MM-DD').format('DD/MM/YYYY');
        dbBirthdate = moment(dbBirthdate.date, dbBirthdate.format).format('DD/MM/YYYY');
        return seBirthdate === dbBirthdate;
    }

    __seasonalityAllowedToBeImportedAsIncomplete (member) {
        const [seasonalityTag] = member.tags.filter(
            (tag) =>
                tag[this.Utils.TAG_FIELD.TYPE] === this.Utils.TAG_NAME.SEASONALITY
        );

        if (_.isEmpty(seasonalityTag)) {
            loggers.errors_log.error('No seasonality tag found for athlete');
            return false;
        }

        let seasonality = seasonalityTag[this.Utils.TAG_FIELD.VALUE]?.value?.trim().toLowerCase();

        return this.Utils.SEASONALITY_ALLOWED_TO_BE_IMPORTED_AS_INCOMPLETE.includes(seasonality);
    }

    __isGenderEqual (dbGender, seGender) {
        return seGender === dbGender;
    }

    __processMembershipSeasonalityValues (memberships) {
        const membershipsWithFullSeasonality = memberships.reduce((items, member) => {
            const [seasonalityTag] = member.tags.filter(
                (tag) =>
                    tag[this.Utils.TAG_FIELD.TYPE] === this.Utils.TAG_NAME.SEASONALITY
            );

            if (!_.isEmpty(seasonalityTag)) {
                const seasonality = seasonalityTag[this.Utils.TAG_FIELD.VALUE]?.value?.trim().toLowerCase();

                if(this.Utils.isSeasonalityFull(seasonality)) {
                    items.push(member);
                }
            }

            return items;
        }, []);

        if(membershipsWithFullSeasonality.length) {
            return membershipsWithFullSeasonality;
        } else {
            return memberships;
        }
    }
}

module.exports = MemberValidationService;
