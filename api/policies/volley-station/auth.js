module.exports = function (req, res, next) {
    const authorization = req.get('Authorization');
    
    if (!authorization) {
        return res.status(401).json({ 
            error: 'Authorization token required',
            message: 'Please provide Authorization header'
        });
    }

    if (authorization === sails.config.volleyStation.apiKey) {
        next();
    } else {
        return res.status(401).json({ 
            error: 'Invalid authorization token',
            message: 'The provided token is not valid'
        });
    }
};
