module.exports = function volleyStationEventAccess(req, res, next) {
    const eventId = parseInt(req.params.eventId);
    
    if (!eventId || isNaN(eventId)) {
        return res.status(400).json({
            error: 'Invalid event ID',
            message: 'Event ID must be a valid number'
        });
    }

    const eventQuery = knex('event as e')
        .select('event_id', 'long_name')
        .where('event_id', eventId)
        .where('enable_volley_station', true);

    Db.query(eventQuery)
        .then(({ rows }) => {
            if (!rows.length) {
                return res.status(403).json({
                    error: 'Event not available for VolleyStation integration',
                    message: 'This event is not configured for VolleyStation access'
                });
            }
            
            // Store event data for use in controller
            req.volleyStationEvent = rows[0];
            next();
        })
        .catch(err => {
            sails.log.error('VolleyStation event access error:', err);
            res.status(500).json({
                error: 'Internal server error',
                message: 'Unable to verify event access'
            });
        });
};
