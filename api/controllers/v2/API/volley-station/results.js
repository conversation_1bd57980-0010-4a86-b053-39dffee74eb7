module.exports = {
    friendlyName: 'Submit match results for VolleyStation',
    description: 'Accepts match results and scores for an event',

    inputs: {
        eventId: {
            type: 'number',
            required: true,
            description: 'The ID of the event'
        },
        matchId: {
            type: 'string',
            required: true,
            description: 'Unique identifier for the match'
        },
        team1Id: {
            type: 'number',
            required: true,
            description: 'ID of team 1'
        },
        team2Id: {
            type: 'number',
            required: true,
            description: 'ID of team 2'
        },
        sets: {
            type: 'ref',
            required: true,
            description: 'Array of set scores'
        },
        matchStatus: {
            type: 'string',
            isIn: ['completed', 'in_progress', 'scheduled'],
            defaultsTo: 'completed',
            description: 'Status of the match'
        },
        winnerId: {
            type: 'number',
            description: 'ID of the winning team (required if status is completed)'
        },
        notes: {
            type: 'string',
            description: 'Additional notes about the match'
        }
    },

    exits: {
        success: {
            statusCode: 200,
            description: 'Match results submitted successfully'
        },
        forbidden: {
            statusCode: 403,
            description: 'Event not available for VolleyStation integration'
        },
        badRequest: {
            statusCode: 400,
            description: 'Invalid request data'
        },
        serverError: {
            statusCode: 500,
            description: 'Internal server error'
        }
    },

    fn: async function (inputs, exits) {
        try {
            // Check if event exists and is enabled for VolleyStation
            const eventQuery = knex('event as e')
                .select('event_id', 'long_name')
                .where('event_id', inputs.eventId)
                .where('enable_volley_station', true)
                .where('published', true);

            const { rows: eventRows } = await Db.query(eventQuery);

            if (!eventRows.length) {
                return exits.forbidden({
                    error: 'Event not available for VolleyStation integration',
                    message: 'This event is not configured for VolleyStation access'
                });
            }

            // Validate input data
            const validationResult = validateMatchResults(inputs);
            if (!validationResult.isValid) {
                return exits.badRequest({
                    error: 'Invalid match data',
                    message: validationResult.message,
                    details: validationResult.errors
                });
            }

            // Mock storage of results - replace with actual storage later
            const resultData = await storeMatchResults(inputs);

            return exits.success({
                message: 'Match results submitted successfully',
                data: {
                    matchId: inputs.matchId,
                    eventId: inputs.eventId,
                    status: inputs.matchStatus,
                    submittedAt: new Date().toISOString(),
                    resultId: resultData.resultId
                }
            });

        } catch (err) {
            sails.log.error('VolleyStation results submission error:', err);
            return exits.serverError({
                error: 'Internal server error',
                message: 'Unable to submit match results'
            });
        }
    }
};

function validateMatchResults(inputs) {
    const errors = [];

    // Validate sets array
    if (!Array.isArray(inputs.sets)) {
        errors.push('Sets must be an array');
    } else {
        inputs.sets.forEach((set, index) => {
            if (!set.team1Score || !set.team2Score) {
                errors.push(`Set ${index + 1} must have scores for both teams`);
            }
            if (typeof set.team1Score !== 'number' || typeof set.team2Score !== 'number') {
                errors.push(`Set ${index + 1} scores must be numbers`);
            }
            if (set.team1Score < 0 || set.team2Score < 0) {
                errors.push(`Set ${index + 1} scores cannot be negative`);
            }
        });
    }

    // Validate winner if match is completed
    if (inputs.matchStatus === 'completed') {
        if (!inputs.winnerId) {
            errors.push('Winner ID is required for completed matches');
        } else if (inputs.winnerId !== inputs.team1Id && inputs.winnerId !== inputs.team2Id) {
            errors.push('Winner ID must match one of the team IDs');
        }
    }

    // Validate team IDs are different
    if (inputs.team1Id === inputs.team2Id) {
        errors.push('Team 1 and Team 2 must be different');
    }

    return {
        isValid: errors.length === 0,
        errors: errors,
        message: errors.length > 0 ? errors.join('; ') : 'Valid'
    };
}

async function storeMatchResults(inputs) {
    // Mock storage implementation - replace with actual database storage later
    const resultId = `result_${inputs.eventId}_${inputs.matchId}_${Date.now()}`;
    
    // Log the submission for now
    sails.log.info('Mock storing match results:', {
        resultId: resultId,
        eventId: inputs.eventId,
        matchId: inputs.matchId,
        team1Id: inputs.team1Id,
        team2Id: inputs.team2Id,
        sets: inputs.sets,
        status: inputs.matchStatus,
        winnerId: inputs.winnerId,
        notes: inputs.notes,
        submittedAt: new Date().toISOString()
    });

    // Return mock result data
    return {
        resultId: resultId,
        stored: true
    };
}
