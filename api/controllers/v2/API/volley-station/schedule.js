module.exports = {
    friendlyName: 'Get event schedule for VolleyStation',
    description: 'Returns paginated schedule data for a specific event',

    inputs: {
        eventId: {
            type: 'number',
            required: true,
            description: 'The ID of the event'
        },
        page: {
            type: 'number',
            defaultsTo: 1,
            description: 'Page number for pagination'
        },
        limit: {
            type: 'number',
            defaultsTo: 50,
            description: 'Items per page (max 100)'
        }
    },

    exits: {
        success: {
            statusCode: 200,
            description: 'Paginated schedule data'
        },
        forbidden: {
            statusCode: 403,
            description: 'Event not available for VolleyStation integration'
        },
        notFound: {
            statusCode: 404,
            description: 'Event not found'
        },
        badRequest: {
            statusCode: 400,
            description: 'Invalid request parameters'
        },
        serverError: {
            statusCode: 500,
            description: 'Internal server error'
        }
    },

    fn: async function (inputs, exits) {
        try {
            // Event access already validated by policy
            const event = this.req.volleyStationEvent;

            // Validate pagination parameters
            const page = Math.max(1, parseInt(inputs.page) || 1);
            const limit = Math.min(100, Math.max(1, parseInt(inputs.limit) || 50));
            const offset = (page - 1) * limit;

            // Get mock schedule data with pagination
            const scheduleData = await getEventSchedule(inputs.eventId, limit, offset);
            const totalCount = await getScheduleCount(inputs.eventId);

            const response = {
                data: scheduleData,
                pagination: {
                    page,
                    limit,
                    total: totalCount,
                    totalPages: Math.ceil(totalCount / limit),
                    hasNext: page * limit < totalCount,
                    hasPrev: page > 1
                }
            };

            return exits.success(response);

        } catch (err) {
            sails.log.error('VolleyStation schedule error:', err);
            return exits.serverError({
                error: 'Internal server error',
                message: 'Unable to retrieve schedule data'
            });
        }
    }
};

async function getEventSchedule(eventId, limit, offset) {
    // Mock schedule data - replace with actual data retrieval later
    const mockMatches = [];
    const totalMockMatches = 150; // Simulate 150 total matches
    
    for (let i = offset; i < Math.min(offset + limit, totalMockMatches); i++) {
        const matchNumber = i + 1;
        mockMatches.push({
            match_uuid: `match_${eventId}_${matchNumber}`,
            event_id: eventId,
            match_id: matchNumber,
            date_time: new Date(Date.now() + (i * 3600000)).toISOString(), // Staggered times
            court: `Court ${(i % 8) + 1}`,
            court_alpha: String.fromCharCode(65 + (i % 8)), // A, B, C, etc.
            team_1_name: `Team Alpha ${matchNumber}`,
            team_2_name: `Team Beta ${matchNumber}`,
            ref_name: `Referee ${(i % 10) + 1}`,
            master_team_id_1: 1000 + (i * 2),
            master_team_id_2: 1000 + (i * 2) + 1,
            pool_name: `Pool ${String.fromCharCode(65 + (i % 4))}`,
            division: `Division ${(i % 3) + 1}`,
            results: null // No results yet for mock data
        });
    }
    
    return mockMatches;
}

async function getScheduleCount(eventId) {
    // Mock total count - replace with actual count query later
    return 150;
}
