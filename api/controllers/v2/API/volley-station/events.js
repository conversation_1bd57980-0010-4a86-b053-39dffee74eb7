module.exports = {
    friendlyName: 'Get VolleyStation events',
    description: 'Returns all events that have enable_volley_station set to true',

    exits: {
        success: {
            statusCode: 200,
            description: 'List of events enabled for VolleyStation integration'
        },
        serverError: {
            statusCode: 500,
            description: 'Internal server error'
        }
    },

    fn: async function (inputs, exits) {
        try {
            const events = await getVolleyStationEvents();
            return exits.success(events);
        } catch (err) {
            sails.log.error('VolleyStation events error:', err);
            return exits.serverError({
                error: 'Internal server error',
                message: 'Unable to retrieve events'
            });
        }
    }
};

async function getVolleyStationEvents() {
    const query = knex('event as e')
        .select('event_id', 'long_name as event_name')
        .where('e.enable_volley_station', true)
        .where('e.published', true)
        .where('e.allow_teams_registration', true)
        .orderBy('e.date_start', 'desc');

    const { rows } = await Db.query(query);
    return rows || [];
}
