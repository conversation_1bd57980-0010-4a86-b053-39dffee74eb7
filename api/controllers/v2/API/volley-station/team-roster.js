module.exports = {
    friendlyName: 'Get team roster data for VolleyStation',
    description: 'Returns paginated team roster data including athletes and staff for a specific event',

    inputs: {
        eventId: {
            type: 'number',
            required: true,
            description: 'The ID of the event'
        },
        page: {
            type: 'number',
            defaultsTo: 1,
            description: 'Page number for pagination'
        },
        limit: {
            type: 'number',
            defaultsTo: 50,
            description: 'Items per page (max 100)'
        }
    },

    exits: {
        success: {
            statusCode: 200,
            description: 'Paginated team roster data'
        },
        forbidden: {
            statusCode: 403,
            description: 'Event not available for VolleyStation integration'
        },
        notFound: {
            statusCode: 404,
            description: 'Event not found'
        },
        badRequest: {
            statusCode: 400,
            description: 'Invalid request parameters'
        },
        serverError: {
            statusCode: 500,
            description: 'Internal server error'
        }
    },

    fn: async function (inputs, exits) {
        try {
            // Event access already validated by policy
            const event = this.req.volleyStationEvent;

            // Validate pagination parameters
            const page = Math.max(1, parseInt(inputs.page) || 1);
            const limit = Math.min(100, Math.max(1, parseInt(inputs.limit) || 50));
            const offset = (page - 1) * limit;

            // Get mock team roster data with pagination
            const rosterData = await getTeamRosterData(inputs.eventId, limit, offset);
            const totalCount = await getRosterCount(inputs.eventId);

            const response = {
                data: rosterData,
                pagination: {
                    page,
                    limit,
                    total: totalCount,
                    totalPages: Math.ceil(totalCount / limit),
                    hasNext: page * limit < totalCount,
                    hasPrev: page > 1
                }
            };

            return exits.success(response);

        } catch (err) {
            sails.log.error('VolleyStation team roster error:', err);
            return exits.serverError({
                error: 'Internal server error',
                message: 'Unable to retrieve team roster data'
            });
        }
    }
};

async function getTeamRosterData(eventId, limit, offset) {
    // Mock team roster data - replace with actual data retrieval later
    const mockTeams = [];
    const totalMockTeams = 80; // Simulate 80 total teams
    
    for (let i = offset; i < Math.min(offset + limit, totalMockTeams); i++) {
        const teamNumber = i + 1;
        
        // Generate mock athletes for this team
        const athletes = [];
        const athleteCount = 12 + (i % 6); // 12-17 athletes per team
        
        for (let j = 0; j < athleteCount; j++) {
            athletes.push({
                athlete_id: (teamNumber * 100) + j + 1,
                first_name: `Player${j + 1}`,
                last_name: `Team${teamNumber}`,
                jersey_number: j + 1,
                position: ['Outside Hitter', 'Middle Blocker', 'Setter', 'Libero', 'Opposite'][j % 5],
                height: `${5 + (j % 2)}'${8 + (j % 5)}"`,
                year: ['Freshman', 'Sophomore', 'Junior', 'Senior'][j % 4],
                hometown: `City${j + 1}, State`
            });
        }
        
        // Generate mock staff for this team
        const staff = [
            {
                staff_id: (teamNumber * 10) + 1,
                first_name: 'Head',
                last_name: `Coach${teamNumber}`,
                role: 'Head Coach',
                email: `headcoach${teamNumber}@example.com`,
                phone: `555-${String(teamNumber).padStart(3, '0')}-0001`
            },
            {
                staff_id: (teamNumber * 10) + 2,
                first_name: 'Assistant',
                last_name: `Coach${teamNumber}`,
                role: 'Assistant Coach',
                email: `assistantcoach${teamNumber}@example.com`,
                phone: `555-${String(teamNumber).padStart(3, '0')}-0002`
            }
        ];
        
        mockTeams.push({
            team_id: 2000 + teamNumber,
            team_name: `Volleyball Team ${teamNumber}`,
            club_name: `Club ${String.fromCharCode(65 + (i % 26))}`,
            division: `Division ${(i % 4) + 1}`,
            pool: `Pool ${String.fromCharCode(65 + (i % 8))}`,
            seed: teamNumber,
            status: 'confirmed',
            athletes: athletes,
            staff: staff,
            team_stats: {
                total_athletes: athletes.length,
                total_staff: staff.length,
                registration_date: new Date(Date.now() - (i * 86400000)).toISOString().split('T')[0]
            }
        });
    }
    
    return mockTeams;
}

async function getRosterCount(eventId) {
    // Mock total count - replace with actual count query later
    return 80;
}
