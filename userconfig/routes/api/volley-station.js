module.exports = {

    /**
     * @api {get} /api/volley-station/v1/events/:eventId/schedule Get event schedule
     * @apiDescription Returns paginated schedule data for a specific event
     * @apiGroup VolleyStation API
     * @apiHeader {String} Authorization VolleyStation API key
     * @apiParam {Number} eventId Event ID
     * @apiParam {Number} [page=1] Page number for pagination
     * @apiParam {Number} [limit=50] Items per page (max 100)
     */
    'GET /api/volley-station/v1/events/:eventId/schedule': {
        action: 'v2/API/volley-station/schedule',
        policy: ['volley-station/auth', 'volley-station/eventAccess']
    },

    /**
     * @api {get} /api/volley-station/v1/events/:eventId/team-roster Get team roster data
     * @apiDescription Returns paginated team roster data including athletes and staff
     * @apiGroup VolleyStation API
     * @apiHeader {String} Authorization VolleyStation API key
     * @apiParam {Number} eventId Event ID
     * @apiParam {Number} [page=1] Page number for pagination
     * @apiParam {Number} [limit=50] Items per page (max 100)
     */
    'GET /api/volley-station/v1/events/:eventId/team-roster': {
        action: 'v2/API/volley-station/team-roster',
        policy: ['volley-station/auth', 'volley-station/eventAccess']
    }

};
