# VolleyStation API Refactor Analysis

## Completed Changes

### ✅ 1. Removed Unused Endpoints
- **Deleted**: `api/controllers/v2/API/volley-station/events.js`
- **Deleted**: `api/controllers/v2/API/volley-station/results.js`
- **Updated**: `userconfig/routes/api/volley-station.js` - removed corresponding routes
- **Result**: Only `/schedule` and `/team-roster` endpoints remain

### ✅ 2. Simplified Access Control Logic
- **Removed**: `published` field check from SQL WHERE clauses
- **Kept**: Only `enable_volley_station = true` validation
- **Result**: HTTP 403 returned only when `enable_volley_station` is false/null

## Shared Validation Patterns Analysis

Based on codebase analysis, here are the established patterns for shared functionality:

### Pattern 1: Policy-Based Approach
**Examples**: `validateEventId.js`, `housing/housingManagerEventAccess.js`
- **Location**: `api/policies/`
- **Usage**: Applied via route configuration
- **Pros**: Automatic execution, consistent across endpoints
- **Cons**: Less flexible, harder to customize error messages

### Pattern 2: Service-Based Approach  
**Examples**: `EventService.js`, `FundsTransferService.js`, `_EventTicketService.js`
- **Location**: `api/services/`
- **Usage**: Called from controllers
- **Pros**: Reusable, testable, flexible
- **Cons**: Must remember to call, potential inconsistency

### Pattern 3: Helper/Utility Approach
**Examples**: Validation functions in services, `swUtils.js`
- **Location**: `api/helpers/` or `api/lib/`
- **Usage**: Imported and called directly
- **Pros**: Lightweight, focused, easy to test
- **Cons**: Manual integration required

## Recommended Approaches for VolleyStation Access Control

### Option A: Custom Policy (Recommended)
**Location**: `api/policies/volley-station/eventAccess.js`

```javascript
module.exports = function volleyStationEventAccess(req, res, next) {
    const eventId = parseInt(req.params.eventId);
    
    if (!eventId || isNaN(eventId)) {
        return res.status(400).json({
            error: 'Invalid event ID',
            message: 'Event ID must be a valid number'
        });
    }

    const eventQuery = knex('event as e')
        .select('event_id', 'long_name')
        .where('event_id', eventId)
        .where('enable_volley_station', true);

    Db.query(eventQuery)
        .then(({ rows }) => {
            if (!rows.length) {
                return res.status(403).json({
                    error: 'Event not available for VolleyStation integration',
                    message: 'This event is not configured for VolleyStation access'
                });
            }
            
            // Store event data for use in controller
            req.volleyStationEvent = rows[0];
            next();
        })
        .catch(err => {
            sails.log.error('VolleyStation event access error:', err);
            res.status(500).json({
                error: 'Internal server error',
                message: 'Unable to verify event access'
            });
        });
};
```

**Route Configuration**:
```javascript
// userconfig/routes/api/volley-station.js
'GET /api/volley-station/v1/events/:eventId/schedule': { 
    action: 'v2/API/volley-station/schedule',
    policy: ['volley-station/auth', 'volley-station/eventAccess']
},
```

**Controller Usage**:
```javascript
// Controllers can access req.volleyStationEvent
fn: async function (inputs, exits) {
    // Event access already validated by policy
    const event = this.req.volleyStationEvent;
    
    // Continue with business logic...
}
```

**Benefits**:
- ✅ Automatic execution via route configuration
- ✅ Consistent across all endpoints
- ✅ Follows existing codebase patterns
- ✅ Event data available in controllers
- ✅ Centralized error handling

**Drawbacks**:
- ❌ Less flexible for custom validation logic
- ❌ Harder to unit test in isolation

### Option B: Service-Based Approach
**Location**: `api/services/volley-station/EventAccessService.js`

```javascript
class VolleyStationEventAccessService {
    
    async validateEventAccess(eventId) {
        if (!eventId || isNaN(parseInt(eventId))) {
            throw {
                status: 400,
                error: 'Invalid event ID',
                message: 'Event ID must be a valid number'
            };
        }

        const eventQuery = knex('event as e')
            .select('event_id', 'long_name')
            .where('event_id', parseInt(eventId))
            .where('enable_volley_station', true);

        const { rows } = await Db.query(eventQuery);

        if (!rows.length) {
            throw {
                status: 403,
                error: 'Event not available for VolleyStation integration',
                message: 'This event is not configured for VolleyStation access'
            };
        }

        return rows[0];
    }
}

module.exports = new VolleyStationEventAccessService();
```

**Controller Usage**:
```javascript
const VolleyStationEventAccessService = require('../../../services/volley-station/EventAccessService');

fn: async function (inputs, exits) {
    try {
        const event = await VolleyStationEventAccessService.validateEventAccess(inputs.eventId);
        
        // Continue with business logic...
    } catch (err) {
        if (err.status) {
            return exits[err.status === 403 ? 'forbidden' : 'badRequest'](err);
        }
        throw err;
    }
}
```

**Benefits**:
- ✅ Highly reusable and testable
- ✅ Flexible error handling
- ✅ Easy to extend with additional logic
- ✅ Clear separation of concerns

**Drawbacks**:
- ❌ Must remember to call in each controller
- ❌ Potential for inconsistent usage
- ❌ More boilerplate code

### Option C: Helper Function Approach
**Location**: `api/helpers/volley-station/validateEventAccess.js`

```javascript
module.exports = {
    friendlyName: 'Validate VolleyStation event access',
    description: 'Validates that an event is enabled for VolleyStation integration',
    
    inputs: {
        eventId: {
            type: 'number',
            required: true,
            description: 'The event ID to validate'
        }
    },
    
    exits: {
        success: {
            description: 'Event is valid and accessible'
        },
        forbidden: {
            description: 'Event is not enabled for VolleyStation'
        },
        badRequest: {
            description: 'Invalid event ID'
        }
    },
    
    fn: async function (inputs, exits) {
        const eventQuery = knex('event as e')
            .select('event_id', 'long_name')
            .where('event_id', inputs.eventId)
            .where('enable_volley_station', true);

        const { rows } = await Db.query(eventQuery);

        if (!rows.length) {
            return exits.forbidden({
                error: 'Event not available for VolleyStation integration',
                message: 'This event is not configured for VolleyStation access'
            });
        }

        return exits.success(rows[0]);
    }
};
```

**Controller Usage**:
```javascript
fn: async function (inputs, exits) {
    const event = await sails.helpers.volleyStation.validateEventAccess(inputs.eventId)
        .intercept('forbidden', () => exits.forbidden({
            error: 'Event not available for VolleyStation integration',
            message: 'This event is not configured for VolleyStation access'
        }));
    
    // Continue with business logic...
}
```

**Benefits**:
- ✅ SailsJS native helper pattern
- ✅ Automatic error handling via intercept
- ✅ Easy to test and reuse
- ✅ Good documentation support

**Drawbacks**:
- ❌ Less common pattern in this codebase
- ❌ Requires manual integration
- ❌ Helper overhead for simple validation

## Final Recommendation

**Use Option A (Custom Policy)** for the following reasons:

1. **Consistency**: Matches existing patterns like `housing/housingManagerEventAccess.js`
2. **Automatic Execution**: No risk of forgetting to validate
3. **Performance**: Single database query per request
4. **Maintainability**: Centralized logic, easy to update
5. **Integration**: Follows SailsJS policy patterns used throughout the codebase

The policy approach is the most appropriate for this use case because:
- Event access validation is a cross-cutting concern
- It should happen automatically for all VolleyStation endpoints
- It follows the established pattern used by other API integrations
- It provides consistent error responses across endpoints

## Implementation Steps

1. Create `api/policies/volley-station/eventAccess.js`
2. Update route configurations to include the new policy
3. Remove duplicate validation code from controllers
4. Update controllers to use `req.volleyStationEvent` data
5. Test all endpoints to ensure proper access control
